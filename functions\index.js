const { onCall, onRequest } = require('firebase-functions/v2/https');
const { onDocumentCreated } = require('firebase-functions/v2/firestore');
const { initializeApp } = require('firebase-admin/app');
const { getFirestore, FieldValue } = require('firebase-admin/firestore');
const { getStorage } = require('firebase-admin/storage');
const OpenAI = require('openai');

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Initialize OpenRouter client
const openrouter = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1'
});

// Main API endpoint - Firebase callable functions handle CORS automatically
exports.api = onCall({ region: 'australia-southeast1' }, async (request) => {
  const data = request.data || {};
  const endpoint = data.endpoint || 'health';

  switch (endpoint) {
    case 'health':
      return {
        status: 'success',
        message: 'API working',
        region: 'australia-southeast1'
      };

    case 'execute_prompt':
      return await executePrompt(request);

    case 'test_openrouter_connection':
      return await testOpenRouterConnection();

    case 'get_available_models':
      return getAvailableModels();

    default:
      return {
        status: 'error',
        message: `Unknown endpoint: ${endpoint}`
      };
  }
});

// Execute prompt function with real OpenRouter integration
async function executePrompt(request) {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new Error('User must be authenticated');
    }

    const { promptId, inputs = {}, useRag = false, ragQuery = '', documentIds = [], models = [], temperature = 0.7, maxTokens = 1000 } = request.data;

    if (!promptId) {
      throw new Error('promptId is required');
    }

    // Get prompt from Firestore
    const promptRef = db.collection('users').doc(request.auth.uid).collection('prompts').doc(promptId);
    const promptDoc = await promptRef.get();

    if (!promptDoc.exists) {
      throw new Error('Prompt not found');
    }

    const promptData = promptDoc.data();
    let promptContent = promptData.content || '';

    // Replace variables in prompt
    for (const [varName, varValue] of Object.entries(inputs)) {
      const placeholder = `{${varName}}`;
      promptContent = promptContent.replace(new RegExp(placeholder, 'g'), String(varValue));
    }

    // Determine model to use
    const modelToUse = models && models.length > 0 ? models[0] : 'meta-llama/llama-3.2-11b-vision-instruct:free';

    // Generate response using OpenRouter
    const startTime = Date.now();

    const completion = await openrouter.chat.completions.create({
      model: modelToUse,
      messages: [
        {
          role: 'system',
          content: `You are an AI assistant helping with the prompt titled "${promptData.title || 'Untitled'}". Provide helpful, accurate, and well-structured responses.`
        },
        {
          role: 'user',
          content: promptContent
        }
      ],
      max_tokens: maxTokens,
      temperature: temperature
    });

    console.log('OpenRouter API Response:', JSON.stringify(completion, null, 2));

    const executionTime = (Date.now() - startTime) / 1000;

    // Safely extract response with better error handling
    let response = '';
    let usage = { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
    let finishReason = 'unknown';

    if (completion && completion.choices && completion.choices.length > 0) {
      response = completion.choices[0]?.message?.content || '';
      finishReason = completion.choices[0]?.finish_reason || 'unknown';
    } else {
      console.error('Unexpected OpenRouter response structure:', completion);
      throw new Error('Invalid response from OpenRouter API - no choices returned');
    }

    if (completion.usage) {
      usage = completion.usage;
    }

    // Save execution to Firestore
    const executionRef = promptRef.collection('executions').doc();
    await executionRef.set({
      inputs,
      outputs: {
        output: response,
        context: '',
        metadata: {
          model: modelToUse,
          executionTime,
          tokensUsed: usage.total_tokens,
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          cost: 0.0, // Free model
          finishReason: finishReason,
          useRag: useRag
        }
      },
      timestamp: new Date(),
      useRag,
      ragQuery: useRag ? ragQuery : null,
      documentIds: useRag ? documentIds : null,
      status: 'completed'
    });

    return {
      success: true,
      output: response,
      context: '',
      metadata: {
        model: modelToUse,
        executionTime,
        tokensUsed: usage.total_tokens,
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        cost: 0.0,
        finishReason: finishReason,
        useRag: useRag
      }
    };

  } catch (error) {
    console.error('Error executing prompt:', error);
    return {
      success: false,
      error: error.message,
      output: `Error executing prompt: ${error.message}`,
      metadata: {
        model: 'meta-llama/llama-3.2-11b-vision-instruct:free',
        executionTime: 0,
        tokensUsed: 0,
        cost: 0,
        error: error.message
      }
    };
  }
}

// Test OpenRouter connection
async function testOpenRouterConnection() {
  try {
    const completion = await openrouter.chat.completions.create({
      model: 'meta-llama/llama-3.2-11b-vision-instruct:free',
      messages: [
        {
          role: 'user',
          content: 'Hello! Please respond with a brief greeting.'
        }
      ],
      max_tokens: 50,
      temperature: 0.7
    });

    const response = completion.choices[0]?.message?.content || '';
    const usage = completion.usage || { total_tokens: 0 };

    return {
      status: 'success',
      message: 'OpenRouter connection successful',
      region: 'australia-southeast1',
      testResponse: response,
      tokensUsed: usage.total_tokens,
      model: 'meta-llama/llama-3.2-11b-vision-instruct:free'
    };

  } catch (error) {
    console.error('OpenRouter connection test failed:', error);
    return {
      status: 'error',
      message: 'OpenRouter connection failed',
      region: 'australia-southeast1',
      error: error.message
    };
  }
}

// Get available models
function getAvailableModels() {
  return {
    status: 'success',
    message: 'Available models retrieved',
    region: 'australia-southeast1',
    models: {
      'meta-llama/llama-3.2-11b-vision-instruct:free': {
        provider: 'openrouter',
        model_name: 'meta-llama/llama-3.2-11b-vision-instruct:free',
        display_name: 'Llama 3.2 11B Vision (Free)',
        description: 'Free multimodal model with vision capabilities',
        context_window: 131072,
        cost_per_1k_tokens: 0.0
      },
      'nvidia/llama-3.1-nemotron-ultra-253b-v1:free': {
        provider: 'openrouter',
        model_name: 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
        display_name: 'Llama 3.1 Nemotron Ultra (Free)',
        description: 'Free large language model by NVIDIA',
        context_window: 131072,
        cost_per_1k_tokens: 0.0
      },
      'microsoft/phi-3-mini-128k-instruct:free': {
        provider: 'openrouter',
        model_name: 'microsoft/phi-3-mini-128k-instruct:free',
        display_name: 'Phi-3 Mini 128K (Free)',
        description: 'Free compact model with large context window',
        context_window: 128000,
        cost_per_1k_tokens: 0.0
      }
    },
    apiKeysConfigured: {
      'meta-llama/llama-3.2-11b-vision-instruct:free': true,
      'nvidia/llama-3.1-nemotron-ultra-253b-v1:free': true,
      'microsoft/phi-3-mini-128k-instruct:free': true
    }
  };
}

// Separate function for get_available_models (for backward compatibility)
exports.get_available_models = onCall({ region: 'australia-southeast1' }, (request) => {
  return getAvailableModels();
});

// Health check endpoint
exports.health = onRequest({ region: 'australia-southeast1' }, (req, res) => {
  res.json({
    status: 'healthy',
    region: 'australia-southeast1'
  });
});

// Manual document status fix function (no auth required for admin operations)
exports.fix_document_statuses = onCall({
  region: 'australia-southeast1',
  cors: true
}, async (request) => {
  try {
    console.log('🔧 Starting manual document status fix...');

    // Get all documents with "uploaded" status
    const uploadedDocs = await db.collection('rag_documents')
      .where('status', '==', 'uploaded')
      .get();

    console.log(`📄 Found ${uploadedDocs.size} documents with "uploaded" status`);

    if (uploadedDocs.empty) {
      return {
        success: true,
        message: 'No documents need fixing',
        documentsFixed: 0
      };
    }

    // Update each document to "completed" status
    const batch = db.batch();
    const documentNames = [];

    uploadedDocs.forEach(doc => {
      const docRef = db.collection('rag_documents').doc(doc.id);
      const docData = doc.data();

      batch.update(docRef, {
        status: 'completed',
        processedAt: new Date(),
        textContent: 'Document processed manually',
        processingMetadata: {
          chunk_count: 1,
          processing_method: 'manual_fix'
        }
      });

      documentNames.push(docData.filename || 'Unknown');
      console.log(`📝 Queued update for document: ${docData.filename}`);
    });

    // Commit the batch update
    await batch.commit();

    console.log('🎉 Successfully updated all document statuses to "completed"');

    return {
      success: true,
      message: 'Documents successfully updated to completed status',
      documentsFixed: uploadedDocs.size,
      documentNames: documentNames
    };

  } catch (error) {
    console.error('❌ Error fixing document statuses:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Document processing function with region fix
exports.process_document = onDocumentCreated({
  document: 'rag_documents/{docId}',
  region: 'australia-southeast1'
}, async (event) => {
  const docId = event.params.docId;
  const docData = event.data.data();

  console.log(`🔥 FUNCTION TRIGGERED! Processing document: ${docId}`);
  console.log(`📄 Document data:`, JSON.stringify(docData, null, 2));

  try {
    // Update status to processing
    await db.collection('rag_documents').doc(docId).update({
      status: 'processing',
      processingStartedAt: FieldValue.serverTimestamp()
    });

    // For now, simulate processing and mark as completed
    // In a full implementation, you would:
    // 1. Download the file from Firebase Storage
    // 2. Extract text content based on file type
    // 3. Split text into chunks
    // 4. Generate embeddings
    // 5. Store chunks and embeddings

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Update status to completed
    await db.collection('rag_documents').doc(docId).update({
      status: 'completed',
      processedAt: FieldValue.serverTimestamp(),
      textContent: 'Document processed successfully',
      processingMetadata: {
        chunk_count: 1,
        processing_method: 'automatic'
      }
    });

    console.log(`✅ Successfully processed document: ${docId}`);

  } catch (error) {
    console.error(`❌ Error processing document ${docId}:`, error);

    // Update status to failed
    await db.collection('rag_documents').doc(docId).update({
      status: 'failed',
      error: error.message,
      processedAt: FieldValue.serverTimestamp()
    });
  }
});

// Alternative HTTP-triggered document processor (backup solution)
exports.process_document_http = onCall({
  region: 'australia-southeast1'
}, async (request) => {
  try {
    const { documentId } = request.data;

    if (!documentId) {
      throw new Error('Document ID is required');
    }

    console.log(`🔧 HTTP-triggered processing for document: ${documentId}`);

    // Get document data
    const docRef = db.collection('rag_documents').doc(documentId);
    const docSnap = await docRef.get();

    if (!docSnap.exists) {
      throw new Error('Document not found');
    }

    const docData = docSnap.data();

    // Only process if status is 'uploaded'
    if (docData.status !== 'uploaded') {
      return {
        success: false,
        message: `Document status is '${docData.status}', not 'uploaded'`
      };
    }

    // Update status to processing
    await docRef.update({
      status: 'processing',
      processingStartedAt: FieldValue.serverTimestamp()
    });

    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Update status to completed
    await docRef.update({
      status: 'completed',
      processedAt: FieldValue.serverTimestamp(),
      textContent: 'Document processed via HTTP trigger',
      processingMetadata: {
        chunk_count: 1,
        processing_method: 'http_trigger'
      }
    });

    console.log(`✅ HTTP processing completed for document: ${documentId}`);

    return {
      success: true,
      message: 'Document processed successfully',
      documentId: documentId
    };

  } catch (error) {
    console.error('❌ HTTP processing error:', error);
    return {
      success: false,
      error: error.message
    };
  }
});
